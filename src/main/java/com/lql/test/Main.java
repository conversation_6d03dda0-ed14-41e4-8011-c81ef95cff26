package com.lql.test;

import org.hyperledger.besu.plugin.BesuPlugin;
import org.hyperledger.besu.plugin.ServiceManager;
import org.hyperledger.besu.plugin.data.BlockHeader;
import org.hyperledger.besu.plugin.data.PropagatedBlockContext;
import org.hyperledger.besu.plugin.services.BesuEvents;
import org.hyperledger.besu.plugin.services.MetricsSystem;
import org.hyperledger.besu.plugin.services.PicoCLIOptions;
import org.hyperledger.besu.plugin.services.metrics.Counter;
import org.hyperledger.besu.plugin.services.metrics.LabelledMetric;
import org.hyperledger.besu.plugin.services.metrics.MetricCategory;
import org.hyperledger.besu.plugin.services.metrics.MetricCategoryRegistry;
import com.google.auto.service.AutoService;
import picocli.CommandLine;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@AutoService(BesuPlugin.class)
public class Main implements BesuPlugin {
  private static final String PLUGIN_NAME = "sample-besu-plugin";

  // Plugin services
  private ServiceManager context;
  private BesuEvents besuEvents;
  private MetricsSystem metricsSystem;

  // Custom metrics
  private LabelledMetric<Counter> blockCounter;
  private Counter totalBlocksProcessed;

  // Event listener
  private long blockEventListenerId;

  // CLI names must be of the form "--plugin-<namespace>-...."
  @CommandLine.Option(names = "--plugin-gas-spending-metrics-name")
  public String metricName = "gas_spending_metrics";


  // Custom metric category
  private final MetricCategory SAMPLE_CATEGORY =
    new MetricCategory() {
      @Override
      public String getName() {
        return metricName;
      }

      @Override
      public Optional<String> getApplicationPrefix() {
        return Optional.empty();
      }
    };

  @Override
  public Optional<String> getName() {
    return Optional.of(PLUGIN_NAME);
  }

  @Override
  public void beforeExternalServices() {
    BesuPlugin.super.beforeExternalServices();
  }

  @Override
  public void register(ServiceManager context) {
    this.context = context;

    // Register CLI options
    context.getService(PicoCLIOptions.class)
      .ifPresent(picoCLIOptions -> picoCLIOptions.addPicoCLIOptions(PLUGIN_NAME, this));

    // Register custom metric category
    context.getService(MetricCategoryRegistry.class)
      .ifPresent(registry -> registry.addMetricCategory(SAMPLE_CATEGORY));

    System.out.println("[" + PLUGIN_NAME + "] Plugin registered successfully");
  }

  @Override
  public void start() {
    // Get metrics system service
    metricsSystem = context.getService(MetricsSystem.class).orElse(null);
    if (metricsSystem != null) {
      initializeMetrics();
    }

    // Get events service and register listeners
    besuEvents = context.getService(BesuEvents.class).orElse(null);
    if (besuEvents != null) {
      registerEventListeners();
    }

    System.out.println("[" + PLUGIN_NAME + "] Plugin started successfully");
  }

  @Override
  public void afterExternalServicePostMainLoop() {
    BesuPlugin.super.afterExternalServicePostMainLoop();
  }

  @Override
  public CompletableFuture<Void> reloadConfiguration() {
    System.out.println("[" + PLUGIN_NAME + "] Configuration reload requested");
    return CompletableFuture.completedFuture(null);
  }

  @Override
  public void stop() {
    if (besuEvents != null && blockEventListenerId != 0) {
      besuEvents.removeBlockPropagatedListener(blockEventListenerId);
    }

    System.out.println("[" + PLUGIN_NAME + "] Plugin stopped successfully");
  }

  @Override
  public String getVersion() {
    return BesuPlugin.super.getVersion();
  }

  private void initializeMetrics() {
    // Create labeled counter for blocks by type
    blockCounter = metricsSystem.createLabelledCounter(
      SAMPLE_CATEGORY,
        "blocks_total",
      "Total number of blocks processed by type",
      "block_type"
    );

    // Create simple counter for total blocks
    totalBlocksProcessed = metricsSystem.createCounter(
      SAMPLE_CATEGORY,
       "blocks_processed_total",
      "Total number of blocks processed"
    );

    System.out.println("[" + PLUGIN_NAME + "] Metrics initialized with prefix: ");
  }

  private void registerEventListeners() {
    // Listen to block propagation events
    blockEventListenerId = besuEvents.addBlockPropagatedListener(this::onBlockPropagated);

    System.out.println("[" + PLUGIN_NAME + "] Event listeners registered");
  }

  private void onBlockPropagated(PropagatedBlockContext blockContext) {
    BlockHeader blockHeader = blockContext.getBlockHeader();

    // Update metrics
    if (blockCounter != null) {
      blockCounter.labels("propagated").inc();
    }
    if (totalBlocksProcessed != null) {
      totalBlocksProcessed.inc();
    }

    // Log block information
    System.out.printf(
      "[%s] Block propagated - Number: %d, Hash: %s, Gas Used: %d%n",
      PLUGIN_NAME,
      blockHeader.getNumber(),
      blockHeader.getBlockHash().toHexString(),
      blockHeader.getGasUsed()
    );
  }
}
