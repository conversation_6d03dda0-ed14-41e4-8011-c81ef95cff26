plugins {
    id 'java'
}

group = 'org.example'
version = '1.0-SNAPSHOT'

sourceCompatibility = '21'

repositories {
    maven { url 'https://hyperledger.jfrog.io/artifactory/besu-maven' }
    mavenCentral()
}

dependencies {
    implementation("org.hyperledger.besu:plugin-api:24.12.2")
    // https://mvnrepository.com/artifact/com.google.auto.service/auto-service
    implementation("com.google.auto.service:auto-service:1.1.1")
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

test {
    useJUnitPlatform()
}